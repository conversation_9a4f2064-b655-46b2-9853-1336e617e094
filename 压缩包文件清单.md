# 股票数据管理系统 - 压缩包文件清单

## 📦 压缩包信息
- **文件名**: `股票数据管理系统.zip`
- **大小**: 23.3 KB
- **创建时间**: 2025年6月8日

## 📁 包含文件列表

### 🎨 GUI应用程序 (2个版本)
1. **`stock_data_flet_gui.py`** - 现代化Flet版本GUI ⭐ **推荐**
   - Material Design风格界面
   - 流畅动画和响应式设计
   - 现代化控件和交互体验

2. **`stock_data_gui.py`** - 经典Tkinter版本GUI
   - 传统桌面应用风格
   - 快速启动，兼容性好
   - 适合企业环境

### 🔧 核心业务模块
3. **`database_config.py`** - 数据库配置管理
   - ClickHouse连接配置
   - 配置文件读写
   - 连接测试功能

4. **`stock_data_manager.py`** - 核心业务逻辑
   - 股票列表生成
   - 日线数据更新
   - 分时数据更新
   - 进度回调和日志记录

### 🚀 启动脚本
5. **`run_flet_app.bat`** - Flet版本启动脚本 (Windows)
   - 自动检查Python环境
   - 自动安装依赖包
   - 启动现代化版本

6. **`run_app.bat`** - Tkinter版本启动脚本 (Windows)
   - 自动检查Python环境
   - 自动安装依赖包
   - 启动经典版本

### 📄 配置和文档
7. **`requirements.txt`** - Python依赖包列表
   - pandas>=1.3.0
   - clickhouse-driver>=0.2.0
   - mootdx>=1.0.0
   - tqdm>=4.60.0
   - flet>=0.21.0

8. **`README.md`** - 详细使用说明
   - 功能特性介绍
   - 安装和使用指南
   - 故障排除说明

9. **`VERSION_COMPARISON.md`** - 版本对比说明
   - Flet vs Tkinter详细对比
   - 使用场景推荐
   - 界面特色说明

### 📚 原始程序文件
10. **`01文件夹中取股票列表名.py`** - 原始股票列表生成程序
    - 扫描通达信数据文件
    - 生成股票代码列表

11. **`02ch通达信日线数据下载_更新用.py`** - 原始日线数据程序
    - 下载和更新日K线数据
    - 防重复插入机制

12. **`03ch通达信分时数据下载_更新用.py`** - 原始分时数据程序
    - 下载和更新分钟级数据
    - 增量更新功能

## 🎯 快速开始

### 方法1: 使用启动脚本 (推荐)
```bash
# 解压后双击运行
run_flet_app.bat    # 现代化版本
run_app.bat         # 经典版本
```

### 方法2: 手动启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行应用
python stock_data_flet_gui.py    # 现代化版本
python stock_data_gui.py         # 经典版本
```

## 💡 使用建议

### 🌟 推荐使用Flet版本，因为：
- 界面更美观现代
- 用户体验更好
- 功能完全相同
- 未来扩展性更强

### 🔧 选择Tkinter版本，如果：
- 需要快速启动
- 对程序大小敏感
- 偏好传统界面
- 企业环境兼容性要求

## 📋 系统要求
- **Python**: 3.7 或更高版本
- **操作系统**: Windows/Linux/macOS
- **通达信**: 需要安装通达信金融终端
- **ClickHouse**: 需要可访问的ClickHouse数据库

## 🔄 版本兼容性
- 两个GUI版本使用相同的核心逻辑
- 配置文件完全兼容
- 可以随时切换使用
- 数据文件通用

## 📞 技术支持
如遇问题，请查看：
1. README.md 中的详细说明
2. VERSION_COMPARISON.md 中的版本对比
3. 应用程序日志文件 (stock_data.log)

---
**注意**: 首次使用前请确保已正确配置ClickHouse数据库连接和通达信安装路径。
