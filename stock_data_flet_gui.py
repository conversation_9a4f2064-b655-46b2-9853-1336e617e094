"""
Stock Data Management GUI Application using Flet
A modern, beautiful interface for managing stock data downloads and updates.
"""

import flet as ft
import threading
import logging
from datetime import datetime
import os
from database_config import DatabaseConfig
from stock_data_manager import StockDataManager


class StockDataFletApp:
    """Modern Flet-based GUI application for stock data management."""
    
    def __init__(self):
        # Initialize components
        self.db_config = DatabaseConfig()
        self.data_manager = StockDataManager(self.db_config)
        
        # Set up callbacks
        self.data_manager.set_progress_callback(self.update_progress)
        self.data_manager.set_log_callback(self.log_message)
        
        # Initialize logging
        self.setup_logging()
        
        # UI components
        self.page = None
        self.progress_bar = None
        self.progress_text = None
        self.log_column = None
        self.stats_text = None
        self.stock_count_text = None
        
        # Form fields
        self.host_field = None
        self.port_field = None
        self.user_field = None
        self.password_field = None
        self.database_field = None
        self.tdx_path_field = None
        self.max_workers_field = None
        self.data_type_radio = None
    
    def setup_logging(self):
        """Set up logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('stock_data.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def main(self, page: ft.Page):
        """Main entry point for the Flet application."""
        self.page = page
        page.title = "股票数据管理系统"
        page.theme_mode = ft.ThemeMode.LIGHT
        page.window_width = 1000
        page.window_height = 800
        page.window_min_width = 800
        page.window_min_height = 600
        
        # Set theme colors
        page.theme = ft.Theme(
            color_scheme_seed=ft.Colors.BLUE,
            visual_density=ft.VisualDensity.COMFORTABLE,
        )
        
        # Create the main layout
        self.create_layout()
        
        # Load saved configuration
        self.load_config_to_gui()
        
        page.update()
    
    def create_layout(self):
        """Create the main application layout."""
        # Create tabs
        tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="数据库配置",
                    icon=ft.Icons.SETTINGS,
                    content=self.create_config_tab()
                ),
                ft.Tab(
                    text="数据管理",
                    icon=ft.Icons.DATA_USAGE,
                    content=self.create_data_tab()
                ),
                ft.Tab(
                    text="系统日志",
                    icon=ft.Icons.ARTICLE,
                    content=self.create_log_tab()
                ),
            ],
            expand=1,
        )
        
        # Main container
        main_container = ft.Container(
            content=tabs,
            padding=20,
            expand=True,
        )
        
        self.page.add(main_container)
    
    def create_config_tab(self):
        """Create database configuration tab."""
        # Database configuration section
        self.host_field = ft.TextField(
            label="主机地址",
            hint_text="例如: ***********",
            prefix_icon=ft.Icons.COMPUTER,
            border_radius=10,
        )

        self.port_field = ft.TextField(
            label="端口",
            hint_text="例如: 9000",
            prefix_icon=ft.Icons.NETWORK_PING,
            border_radius=10,
            keyboard_type=ft.KeyboardType.NUMBER,
        )

        self.user_field = ft.TextField(
            label="用户名",
            hint_text="数据库用户名",
            prefix_icon=ft.Icons.PERSON,
            border_radius=10,
        )

        self.password_field = ft.TextField(
            label="密码",
            hint_text="数据库密码",
            prefix_icon=ft.Icons.LOCK,
            password=True,
            can_reveal_password=True,
            border_radius=10,
        )

        self.database_field = ft.TextField(
            label="数据库名",
            hint_text="例如: default",
            prefix_icon=ft.Icons.STORAGE,
            border_radius=10,
        )
        
        # TongDaXin configuration
        self.tdx_path_field = ft.TextField(
            label="通达信安装路径",
            hint_text="例如: D:/通达信金融终端",
            prefix_icon=ft.Icons.FOLDER,
            border_radius=10,
            expand=True,
        )

        browse_button = ft.ElevatedButton(
            text="浏览",
            icon=ft.Icons.FOLDER_OPEN,
            on_click=self.browse_tdx_path,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
            )
        )

        # Performance settings
        self.max_workers_field = ft.TextField(
            label="最大线程数",
            hint_text="例如: 10",
            prefix_icon=ft.Icons.SPEED,
            border_radius=10,
            keyboard_type=ft.KeyboardType.NUMBER,
            width=200,
        )

        # Buttons
        test_button = ft.ElevatedButton(
            text="测试连接",
            icon=ft.Icons.WIFI_PROTECTED_SETUP,
            on_click=self.test_connection,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.GREEN,
                shape=ft.RoundedRectangleBorder(radius=10),
            )
        )

        save_button = ft.ElevatedButton(
            text="保存配置",
            icon=ft.Icons.SAVE,
            on_click=self.save_config,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.BLUE,
                shape=ft.RoundedRectangleBorder(radius=10),
            )
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("ClickHouse 数据库配置", size=20, weight=ft.FontWeight.BOLD),
                            ft.Divider(),
                            ft.Row([self.host_field, self.port_field]),
                            ft.Row([self.user_field, self.password_field]),
                            self.database_field,
                            ft.Row([test_button, save_button], alignment=ft.MainAxisAlignment.CENTER),
                        ]),
                        padding=20,
                    ),
                    elevation=5,
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("通达信配置", size=20, weight=ft.FontWeight.BOLD),
                            ft.Divider(),
                            ft.Row([self.tdx_path_field, browse_button], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        ]),
                        padding=20,
                    ),
                    elevation=5,
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("性能设置", size=20, weight=ft.FontWeight.BOLD),
                            ft.Divider(),
                            self.max_workers_field,
                        ]),
                        padding=20,
                    ),
                    elevation=5,
                ),
            ], scroll=ft.ScrollMode.AUTO),
            padding=10,
        )

    def create_data_tab(self):
        """Create data management tab."""
        # Stock list management
        generate_stock_button = ft.ElevatedButton(
            text="生成股票列表",
            icon=ft.Icons.LIST_ALT,
            on_click=self.generate_stock_list,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.ORANGE,
                shape=ft.RoundedRectangleBorder(radius=10),
            )
        )

        self.stock_count_text = ft.Text(
            "股票数量: 未知",
            size=16,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_700,
        )

        # Data type selection
        self.data_type_radio = ft.RadioGroup(
            content=ft.Column([
                ft.Radio(value="daily", label="日线数据 (K线数据)"),
                ft.Radio(value="minute", label="分时数据 (分钟级数据)"),
            ]),
            value="daily",
        )

        # Update button
        update_button = ft.ElevatedButton(
            text="开始更新数据",
            icon=ft.Icons.CLOUD_DOWNLOAD,
            on_click=self.start_data_update,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.PURPLE,
                shape=ft.RoundedRectangleBorder(radius=10),
            ),
            width=200,
            height=50,
        )

        # Progress components
        self.progress_bar = ft.ProgressBar(
            width=400,
            color=ft.Colors.BLUE,
            bgcolor=ft.Colors.BLUE_100,
        )

        self.progress_text = ft.Text(
            "就绪",
            size=14,
            color=ft.Colors.BLUE_700,
        )

        # Statistics display
        self.stats_text = ft.Text(
            "等待数据更新...",
            size=12,
            color=ft.Colors.GREY_700,
        )

        return ft.Container(
            content=ft.Column([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("股票列表管理", size=20, weight=ft.FontWeight.BOLD),
                            ft.Divider(),
                            ft.Row([
                                generate_stock_button,
                                self.stock_count_text,
                            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        ]),
                        padding=20,
                    ),
                    elevation=5,
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("数据更新", size=20, weight=ft.FontWeight.BOLD),
                            ft.Divider(),
                            ft.Text("选择数据类型:", size=16, weight=ft.FontWeight.W_500),
                            self.data_type_radio,
                            ft.Container(
                                content=update_button,
                                alignment=ft.alignment.center,
                                padding=10,
                            ),
                        ]),
                        padding=20,
                    ),
                    elevation=5,
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("进度信息", size=20, weight=ft.FontWeight.BOLD),
                            ft.Divider(),
                            self.progress_bar,
                            self.progress_text,
                            ft.Container(height=10),
                            ft.Text("统计信息:", size=16, weight=ft.FontWeight.W_500),
                            ft.Container(
                                content=self.stats_text,
                                bgcolor=ft.Colors.GREY_100,
                                padding=15,
                                border_radius=10,
                                width=400,
                                height=150,
                            ),
                        ]),
                        padding=20,
                    ),
                    elevation=5,
                ),
            ], scroll=ft.ScrollMode.AUTO),
            padding=10,
        )

    def create_log_tab(self):
        """Create logging tab."""
        # Log display
        self.log_column = ft.Column(
            scroll=ft.ScrollMode.AUTO,
            auto_scroll=True,
        )

        # Log controls
        clear_button = ft.ElevatedButton(
            text="清空日志",
            icon=ft.Icons.CLEAR_ALL,
            on_click=self.clear_log,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.RED,
                shape=ft.RoundedRectangleBorder(radius=10),
            )
        )

        save_button = ft.ElevatedButton(
            text="保存日志",
            icon=ft.Icons.SAVE_ALT,
            on_click=self.save_log,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.GREEN,
                shape=ft.RoundedRectangleBorder(radius=10),
            )
        )

        return ft.Container(
            content=ft.Column([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Text("系统日志", size=20, weight=ft.FontWeight.BOLD),
                                ft.Row([clear_button, save_button]),
                            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                            ft.Divider(),
                            ft.Container(
                                content=self.log_column,
                                bgcolor=ft.Colors.BLACK,
                                padding=10,
                                border_radius=10,
                                height=500,
                            ),
                        ]),
                        padding=20,
                    ),
                    elevation=5,
                ),
            ]),
            padding=10,
        )

    def load_config_to_gui(self):
        """Load saved configuration to GUI."""
        db_config = self.db_config.get_db_config()
        self.host_field.value = db_config.get("host", "")
        self.port_field.value = str(db_config.get("port", ""))
        self.user_field.value = db_config.get("user", "")
        self.password_field.value = db_config.get("password", "")
        self.database_field.value = db_config.get("database", "")

        self.tdx_path_field.value = self.db_config.get_tdx_path()
        self.max_workers_field.value = str(self.db_config.get_max_workers())

    def save_config(self, e):
        """Save configuration from GUI."""
        try:
            # Validate port
            port = int(self.port_field.value) if self.port_field.value else 9000
            max_workers = int(self.max_workers_field.value) if self.max_workers_field.value else 10

            # Update database config
            self.db_config.update_db_config(
                host=self.host_field.value,
                port=port,
                user=self.user_field.value,
                password=self.password_field.value,
                database=self.database_field.value or "default"
            )

            # Update other settings
            self.db_config.set_tdx_path(self.tdx_path_field.value)
            self.db_config.set_max_workers(max_workers)

            # Save to file
            if self.db_config.save_config():
                self.show_snack_bar("配置已保存！", ft.Colors.GREEN)
                self.log_message("配置已保存")
            else:
                self.show_snack_bar("保存配置失败！", ft.Colors.RED)

        except ValueError as e:
            self.show_snack_bar(f"配置参数错误：{e}", ft.Colors.RED)
        except Exception as e:
            self.show_snack_bar(f"保存配置时出错：{e}", ft.Colors.RED)

    def test_connection(self, e):
        """Test database connection."""
        try:
            # Update config with current GUI values
            self.save_config(e)

            # Test connection
            success, message = self.db_config.test_connection()

            if success:
                self.show_snack_bar(message, ft.Colors.GREEN)
                self.log_message(f"数据库连接测试成功: {message}")
            else:
                self.show_snack_bar(message, ft.Colors.RED)
                self.log_message(f"数据库连接测试失败: {message}")

        except Exception as e:
            error_msg = f"连接测试出错：{str(e)}"
            self.show_snack_bar(error_msg, ft.Colors.RED)
            self.log_message(error_msg)

    def browse_tdx_path(self, e):
        """Browse for TongDaXin installation path."""
        def get_directory_result(e: ft.FilePickerResultEvent):
            if e.path:
                self.tdx_path_field.value = e.path
                self.page.update()

        file_picker = ft.FilePicker(on_result=get_directory_result)
        self.page.overlay.append(file_picker)
        self.page.update()
        file_picker.get_directory_path(dialog_title="选择通达信安装目录")

    def generate_stock_list(self, e):
        """Generate stock list in a separate thread."""
        def run_generation():
            try:
                success, message, count = self.data_manager.generate_stock_list()

                if success:
                    self.stock_count_text.value = f"股票数量: {count}"
                    self.show_snack_bar(message, ft.Colors.GREEN)
                else:
                    self.show_snack_bar(message, ft.Colors.RED)

                self.page.update()

            except Exception as e:
                error_msg = f"生成股票列表时出错：{str(e)}"
                self.show_snack_bar(error_msg, ft.Colors.RED)
                self.log_message(error_msg)

        threading.Thread(target=run_generation, daemon=True).start()

    def start_data_update(self, e):
        """Start data update in a separate thread."""
        def run_update():
            try:
                self.progress_bar.value = 0
                self.progress_text.value = "准备更新..."
                self.page.update()

                data_type = self.data_type_radio.value

                if data_type == "daily":
                    success, message, stats = self.data_manager.update_daily_data()
                elif data_type == "minute":
                    success, message, stats = self.data_manager.update_minute_data()
                else:
                    success, message, stats = False, "未知的数据类型", {}

                # Display results
                if success:
                    self.display_statistics(stats)
                    self.show_snack_bar(message, ft.Colors.GREEN)
                else:
                    self.show_snack_bar(message, ft.Colors.RED)

                self.progress_bar.value = 1.0
                self.progress_text.value = "完成"
                self.page.update()

            except Exception as e:
                error_msg = f"更新数据时出错：{str(e)}"
                self.show_snack_bar(error_msg, ft.Colors.RED)
                self.log_message(error_msg)

        threading.Thread(target=run_update, daemon=True).start()

    def update_progress(self, current: int, total: int, message: str = ""):
        """Update progress bar and label."""
        if total > 0:
            progress = current / total
            self.progress_bar.value = progress

        if message:
            self.progress_text.value = f"{message} ({current}/{total})"
        else:
            self.progress_text.value = f"进度: {current}/{total}"

        self.page.update()

    def display_statistics(self, stats: dict):
        """Display statistics in the stats text area."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        stats_text = f"更新完成时间: {timestamp}\n\n"

        for key, value in stats.items():
            if key == 'total_rows':
                stats_text += f"数据库总行数: {value:,}\n"
            elif key == 'inserted_count':
                stats_text += f"新增记录数: {value:,}\n"
            elif key == 'skipped_count':
                stats_text += f"跳过记录数: {value:,}\n"
            elif key == 'error_count':
                stats_text += f"错误记录数: {value:,}\n"

        stats_text += "\n" + "="*30
        self.stats_text.value = stats_text
        self.page.update()

    def log_message(self, message: str):
        """Add message to log display."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        log_entry = ft.Container(
            content=ft.Text(
                f"[{timestamp}] {message}",
                color=ft.Colors.GREEN_400,
                size=12,
                font_family="Consolas",
            ),
            padding=ft.padding.symmetric(vertical=2),
        )

        self.log_column.controls.append(log_entry)

        # Keep only last 100 log entries
        if len(self.log_column.controls) > 100:
            self.log_column.controls.pop(0)

        self.page.update()

        # Also log to file
        self.logger.info(message)

    def clear_log(self, e):
        """Clear the log display."""
        self.log_column.controls.clear()
        self.page.update()

    def save_log(self, e):
        """Save log to file."""
        def save_file_result(e: ft.FilePickerResultEvent):
            if e.path:
                try:
                    with open(e.path, 'w', encoding='utf-8') as f:
                        for control in self.log_column.controls:
                            if isinstance(control.content, ft.Text):
                                f.write(control.content.value + "\n")
                    self.show_snack_bar(f"日志已保存到: {e.path}", ft.Colors.GREEN)
                except Exception as ex:
                    self.show_snack_bar(f"保存日志失败: {str(ex)}", ft.Colors.RED)

        file_picker = ft.FilePicker(on_result=save_file_result)
        self.page.overlay.append(file_picker)
        self.page.update()
        file_picker.save_file(
            dialog_title="保存日志文件",
            file_name="stock_data_log.txt",
            allowed_extensions=["txt"]
        )

    def show_snack_bar(self, message: str, color: str):
        """Show a snack bar with the given message and color."""
        snack_bar = ft.SnackBar(
            content=ft.Text(message, color=ft.Colors.WHITE),
            bgcolor=color,
            duration=3000,
        )
        self.page.snack_bar = snack_bar
        snack_bar.open = True
        self.page.update()


def main(page: ft.Page):
    """Main function to run the Flet application."""
    app = StockDataFletApp()
    app.main(page)


if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.FLET_APP)
