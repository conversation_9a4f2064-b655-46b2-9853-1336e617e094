@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo   Stock Data Management System Launcher
echo ========================================
echo.

REM Check if conda is installed
conda --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Conda not found. Please install Anaconda or Miniconda first.
    echo Download from: https://www.anaconda.com/products/distribution
    pause
    exit /b 1
)

echo ✓ Conda found
echo.

REM Get list of conda environments
echo Available Conda Environments:
echo ----------------------------------------
set env_count=0
for /f "skip=2 tokens=1" %%i in ('conda env list') do (
    if not "%%i"=="" (
        set /a env_count+=1
        echo !env_count!. %%i
        set env_!env_count!=%%i
    )
)

if %env_count%==0 (
    echo No conda environments found.
    echo Creating a new environment...
    set /p new_env_name="Enter name for new environment (default: stock_data): "
    if "!new_env_name!"=="" set new_env_name=stock_data
    
    echo Creating conda environment: !new_env_name!
    conda create -n !new_env_name! python=3.9 -y
    if errorlevel 1 (
        echo ERROR: Failed to create conda environment
        pause
        exit /b 1
    )
    set selected_env=!new_env_name!
) else (
    echo.
    set /p env_choice="Select environment number (1-%env_count%): "
    
    REM Validate input
    if !env_choice! lss 1 (
        echo Invalid selection
        pause
        exit /b 1
    )
    if !env_choice! gtr %env_count% (
        echo Invalid selection
        pause
        exit /b 1
    )
    
    set selected_env=!env_%env_choice%!
)

echo.
echo Selected environment: !selected_env!
echo.

REM Activate the selected environment
echo Activating conda environment: !selected_env!
call conda activate !selected_env!
if errorlevel 1 (
    echo ERROR: Failed to activate environment !selected_env!
    pause
    exit /b 1
)

echo ✓ Environment activated: !selected_env!
echo.

REM Check Python version
python --version
echo.

REM Check if requirements are installed
echo Checking dependencies...
set missing_packages=0

REM Check each package from requirements.txt
for /f "tokens=1 delims=>=" %%p in (requirements.txt) do (
    pip show %%p >nul 2>&1
    if errorlevel 1 (
        echo ✗ Missing: %%p
        set missing_packages=1
    ) else (
        echo ✓ Found: %%p
    )
)

echo.

if %missing_packages%==1 (
    echo Some packages are missing.
    set /p install_choice="Install missing packages? (Y/n): "
    if /i "!install_choice!"=="n" (
        echo Installation cancelled by user.
        pause
        exit /b 1
    )
    
    echo.
    echo Installing packages from requirements.txt...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install packages
        pause
        exit /b 1
    )
    echo ✓ All packages installed successfully
) else (
    echo ✓ All required packages are already installed
)

echo.
echo ========================================
echo   Choose Application Version
echo ========================================
echo 1. Modern Flet Version (Recommended)
echo 2. Classic Tkinter Version
echo.
set /p app_choice="Select version (1-2): "

if "!app_choice!"=="1" (
    echo.
    echo Starting Modern Flet Version...
    python stock_data_flet_gui.py
) else if "!app_choice!"=="2" (
    echo.
    echo Starting Classic Tkinter Version...
    python stock_data_gui.py
) else (
    echo Invalid selection. Starting default Flet version...
    python stock_data_flet_gui.py
)

if errorlevel 1 (
    echo.
    echo ========================================
    echo   Application Error Information
    echo ========================================
    echo Error Code: %errorlevel%
    echo.
    echo Possible solutions:
    echo 1. Check if all dependencies are correctly installed
    echo 2. Verify Python version compatibility (3.7+)
    echo 3. Check if ClickHouse database is accessible
    echo 4. Ensure TongDaXin path is correctly configured
    echo.
)

echo.
echo Application finished. Press any key to exit...
pause >nul
