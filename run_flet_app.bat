@echo off
echo 启动现代化股票数据管理系统 (Flet版本)...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show flet >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：安装依赖包失败
        pause
        exit /b 1
    )
)

REM 启动Flet应用程序
echo 启动现代化应用程序...
python stock_data_flet_gui.py

pause
