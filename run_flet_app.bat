@echo off
echo Starting Stock Data Management System (Flet Version)...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7 or higher.
    pause
    exit /b 1
)

REM Check if dependencies are installed
echo Checking dependencies...
pip show flet >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies.
        pause
        exit /b 1
    )
)

REM Start Flet application
echo Starting application...
python stock_data_flet_gui.py

if errorlevel 1 (
    echo.
    echo Program exited with error code: %errorlevel%
    echo Please check:
    echo 1. Python version is 3.7 or higher
    echo 2. All dependencies are correctly installed
    echo 3. No firewall or antivirus blocking the program
    echo.
)

echo Program finished. Press any key to close...
pause
