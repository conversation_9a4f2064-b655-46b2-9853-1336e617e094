@echo off
echo Starting Stock Data Management System (Tkinter Version)...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7 or higher.
    pause
    exit /b 1
)

REM Check if dependencies are installed
echo Checking dependencies...
pip show pandas >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies.
        pause
        exit /b 1
    )
)

REM Start application
echo Starting application...
python stock_data_gui.py

pause
