# 股票数据管理系统

一个综合的GUI应用程序，用于管理股票数据的下载和更新，集成了现有的ClickHouse数据库程序。

## 🎨 两个版本可选

### 1. **现代化版本 (Flet)** - 推荐 ⭐
- **文件**: `stock_data_flet_gui.py`
- **特点**: 现代化界面，美观的Material Design风格
- **启动**: `python stock_data_flet_gui.py` 或 `run_flet_app.bat`

### 2. **经典版本 (Tkinter)**
- **文件**: `stock_data_gui.py`
- **特点**: 传统桌面应用界面，兼容性好
- **启动**: `python stock_data_gui.py` 或 `run_app.bat`

## 功能特性

### 1. 数据库配置界面
- ClickHouse数据库连接参数配置（主机、端口、用户名、密码、数据库名）
- "测试连接"按钮验证数据库连接
- 配置设置的保存/加载功能

### 2. 股票列表管理
- "更新股票列表"按钮执行股票列表更新功能
- 股票列表更新期间显示进度指示器
- 更新操作的成功/错误消息显示

### 3. 数据更新选择界面
- 不同数据类型的选择界面：
  - 日线数据（日K线数据）
  - 分时数据（分钟级/tick数据）
- 使用单选按钮进行数据类型选择
- 历史数据更新的日期范围选择器

### 4. 主要应用流程
- 步骤1：配置和测试数据库连接
- 步骤2：更新股票列表（数据更新的前提条件）
- 步骤3：选择数据类型和更新参数
- 步骤4：执行带进度跟踪的数据更新

### 5. 技术要求
- 使用现代Python GUI框架（tkinter）
- 与工作区中现有的ClickHouse数据库程序集成
- 包含适当的错误处理和用户反馈
- 添加日志功能以跟踪操作

## 安装和使用

### 前提条件
1. Python 3.7+
2. 通达信金融终端已安装
3. ClickHouse数据库服务器运行中

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用程序

#### 现代化版本 (推荐)
```bash
# 直接运行
python stock_data_flet_gui.py

# 或使用启动脚本 (Windows)
run_flet_app.bat
```

#### 经典版本
```bash
# 直接运行
python stock_data_gui.py

# 或使用启动脚本 (Windows)
run_app.bat
```

## 使用说明

### 首次使用
1. **配置数据库连接**：
   - 打开"数据库配置"标签页
   - 填写ClickHouse数据库连接信息
   - 设置通达信安装路径
   - 点击"测试连接"验证配置
   - 点击"保存配置"保存设置

2. **生成股票列表**：
   - 切换到"数据管理"标签页
   - 点击"生成股票列表"按钮
   - 等待股票列表生成完成

3. **更新数据**：
   - 选择要更新的数据类型（日线或分时）
   - 点击"开始更新数据"按钮
   - 在进度区域查看更新进度和统计信息

### 日志查看
- 切换到"日志"标签页查看详细的操作日志
- 可以清空日志或保存日志到文件

## 文件结构

### 🎨 GUI应用程序
- `stock_data_flet_gui.py` - **现代化Flet版本GUI** (推荐)
- `stock_data_gui.py` - 经典Tkinter版本GUI
- `run_flet_app.bat` - Flet版本启动脚本 (Windows)
- `run_app.bat` - Tkinter版本启动脚本 (Windows)

### 🔧 核心模块
- `database_config.py` - 数据库配置管理
- `stock_data_manager.py` - 核心业务逻辑包装器

### 📄 配置和数据文件
- `config.json` - 配置文件（运行后自动生成）
- `requirements.txt` - 依赖项列表
- `stock_data.log` - 应用程序日志文件（运行后自动生成）
- `Stock_List_Update.csv` - 股票列表文件（生成后创建）

### 📚 原始程序文件
- `01文件夹中取股票列表名.py` - 原始股票列表生成程序
- `02ch通达信日线数据下载_更新用.py` - 原始日线数据下载程序
- `03ch通达信分时数据下载_更新用.py` - 原始分时数据下载程序

## 注意事项

1. 确保通达信金融终端路径正确设置
2. 确保ClickHouse数据库服务器可访问
3. 首次运行时需要先生成股票列表
4. 数据更新可能需要较长时间，请耐心等待
5. 建议在网络状况良好时进行数据更新

## 故障排除

### 常见问题
1. **数据库连接失败**：检查ClickHouse服务器是否运行，网络连接是否正常
2. **通达信路径错误**：确保路径指向正确的通达信安装目录
3. **股票列表为空**：检查通达信数据文件是否存在
4. **数据更新失败**：查看日志标签页的详细错误信息

### 日志文件
应用程序会自动生成 `stock_data.log` 文件，包含详细的操作记录和错误信息。

## 技术支持

如遇到问题，请查看日志文件中的详细错误信息，或联系技术支持。
