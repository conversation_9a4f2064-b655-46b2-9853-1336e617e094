"""
Database configuration management module for the Stock Data GUI application.
Handles ClickHouse database connection settings and testing.
"""

import json
import os
from clickhouse_driver import Client
from typing import Dict, Optional, Tuple


class DatabaseConfig:
    """Manages database configuration settings."""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.default_config = {
            "database": {
                "host": "***********",
                "port": 9000,
                "user": "root",
                "password": "root",
                "database": "default"
            },
            "tdx_path": "D:/通达信金融终端",
            "max_workers": 10
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """Load configuration from file or create default."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # Merge with default config to ensure all keys exist
                merged_config = self.default_config.copy()
                merged_config.update(config)
                return merged_config
            except Exception as e:
                print(f"Error loading config: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def get_db_config(self) -> Dict:
        """Get database configuration."""
        return self.config["database"]
    
    def update_db_config(self, host: str, port: int, user: str, password: str, database: str = "default") -> None:
        """Update database configuration."""
        self.config["database"] = {
            "host": host,
            "port": port,
            "user": user,
            "password": password,
            "database": database
        }
    
    def get_tdx_path(self) -> str:
        """Get TongDaXin installation path."""
        return self.config.get("tdx_path", "D:/通达信金融终端")
    
    def set_tdx_path(self, path: str) -> None:
        """Set TongDaXin installation path."""
        self.config["tdx_path"] = path
    
    def get_max_workers(self) -> int:
        """Get maximum number of worker threads."""
        return self.config.get("max_workers", 10)
    
    def set_max_workers(self, workers: int) -> None:
        """Set maximum number of worker threads."""
        self.config["max_workers"] = workers
    
    def test_connection(self) -> Tuple[bool, str]:
        """Test database connection."""
        try:
            db_config = self.get_db_config()
            client = Client(
                host=db_config["host"],
                port=db_config["port"],
                user=db_config["user"],
                password=db_config["password"],
                database=db_config.get("database", "default")
            )
            
            # Test connection by executing a simple query
            result = client.execute("SELECT 1")
            if result and result[0][0] == 1:
                return True, "连接成功！"
            else:
                return False, "连接测试失败：未收到预期响应"
                
        except Exception as e:
            return False, f"连接失败：{str(e)}"
    
    def create_client(self) -> Optional[Client]:
        """Create a ClickHouse client with current configuration."""
        try:
            db_config = self.get_db_config()
            return Client(
                host=db_config["host"],
                port=db_config["port"],
                user=db_config["user"],
                password=db_config["password"],
                database=db_config.get("database", "default")
            )
        except Exception as e:
            print(f"Error creating client: {e}")
            return None
