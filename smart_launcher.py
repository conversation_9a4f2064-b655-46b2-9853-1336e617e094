#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Smart Launcher for Stock Data Management System
Automatically handles conda environment selection and dependency installation
"""

import os
import sys
import subprocess
import json
from pathlib import Path


class SmartLauncher:
    def __init__(self):
        self.requirements_file = "requirements.txt"
        self.apps = {
            "1": {"name": "Modern Flet Version", "file": "stock_data_flet_gui.py", "recommended": True},
            "2": {"name": "Classic Tkinter Version", "file": "stock_data_gui.py", "recommended": False}
        }
    
    def print_header(self):
        """Print application header"""
        print("=" * 60)
        print("   🚀 Stock Data Management System Smart Launcher")
        print("=" * 60)
        print()
    
    def check_conda(self):
        """Check if conda is available"""
        try:
            result = subprocess.run(['conda', '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✓ Conda found: {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ ERROR: Conda not found!")
            print("Please install Anaconda or Miniconda first.")
            print("Download from: https://www.anaconda.com/products/distribution")
            return False
    
    def get_conda_environments(self):
        """Get list of available conda environments"""
        try:
            result = subprocess.run(['conda', 'env', 'list', '--json'], 
                                  capture_output=True, text=True, check=True)
            env_data = json.loads(result.stdout)
            
            environments = []
            for env_path in env_data['envs']:
                env_name = os.path.basename(env_path)
                if env_name == 'base':
                    env_name = f"base ({env_path})"
                environments.append(env_name)
            
            return environments
        except (subprocess.CalledProcessError, json.JSONDecodeError):
            return []
    
    def select_environment(self):
        """Let user select conda environment"""
        environments = self.get_conda_environments()
        
        if not environments:
            print("No conda environments found.")
            return self.create_new_environment()
        
        print("\n📋 Available Conda Environments:")
        print("-" * 40)
        for i, env in enumerate(environments, 1):
            print(f"{i}. {env}")
        
        print(f"{len(environments) + 1}. Create new environment")
        print()
        
        while True:
            try:
                choice = input(f"Select environment (1-{len(environments) + 1}): ").strip()
                choice_num = int(choice)
                
                if 1 <= choice_num <= len(environments):
                    selected_env = environments[choice_num - 1]
                    # Extract environment name (remove path info for base)
                    if selected_env.startswith("base ("):
                        return "base"
                    return selected_env
                elif choice_num == len(environments) + 1:
                    return self.create_new_environment()
                else:
                    print("❌ Invalid selection. Please try again.")
            except ValueError:
                print("❌ Please enter a valid number.")
    
    def create_new_environment(self):
        """Create a new conda environment"""
        default_name = "stock_data"
        env_name = input(f"Enter name for new environment (default: {default_name}): ").strip()
        if not env_name:
            env_name = default_name
        
        print(f"\n🔨 Creating conda environment: {env_name}")
        try:
            subprocess.run(['conda', 'create', '-n', env_name, 'python=3.9', '-y'], 
                         check=True)
            print(f"✓ Environment '{env_name}' created successfully")
            return env_name
        except subprocess.CalledProcessError:
            print(f"❌ Failed to create environment '{env_name}'")
            sys.exit(1)
    
    def activate_environment(self, env_name):
        """Activate conda environment"""
        print(f"\n🔄 Activating environment: {env_name}")
        
        # Get conda activation command
        if os.name == 'nt':  # Windows
            conda_activate = f"conda activate {env_name}"
        else:  # Linux/Mac
            conda_activate = f"source activate {env_name}"
        
        # For Python subprocess, we need to modify the environment
        conda_env_path = self.get_conda_env_path(env_name)
        if conda_env_path:
            # Update PATH to include the conda environment
            env_path = os.path.join(conda_env_path, 'Scripts' if os.name == 'nt' else 'bin')
            os.environ['PATH'] = env_path + os.pathsep + os.environ['PATH']
            print(f"✓ Environment activated: {env_name}")
            return True
        else:
            print(f"❌ Failed to activate environment: {env_name}")
            return False
    
    def get_conda_env_path(self, env_name):
        """Get the path of a conda environment"""
        try:
            result = subprocess.run(['conda', 'env', 'list', '--json'], 
                                  capture_output=True, text=True, check=True)
            env_data = json.loads(result.stdout)
            
            for env_path in env_data['envs']:
                if env_name == 'base' and 'envs' not in env_path:
                    return env_path
                elif os.path.basename(env_path) == env_name:
                    return env_path
            return None
        except (subprocess.CalledProcessError, json.JSONDecodeError):
            return None
    
    def check_requirements(self):
        """Check if all requirements are installed"""
        if not os.path.exists(self.requirements_file):
            print(f"❌ Requirements file '{self.requirements_file}' not found")
            return False
        
        print("\n🔍 Checking dependencies...")
        missing_packages = []
        
        with open(self.requirements_file, 'r') as f:
            requirements = f.read().strip().split('\n')
        
        for requirement in requirements:
            if not requirement.strip() or requirement.startswith('#'):
                continue
            
            package_name = requirement.split('>=')[0].split('==')[0].strip()
            try:
                subprocess.run([sys.executable, '-c', f'import {package_name}'], 
                             check=True, capture_output=True)
                print(f"✓ {package_name}")
            except subprocess.CalledProcessError:
                print(f"✗ {package_name} (missing)")
                missing_packages.append(requirement)
        
        return missing_packages
    
    def install_requirements(self, missing_packages):
        """Install missing requirements"""
        print(f"\n📦 Found {len(missing_packages)} missing packages")
        
        install_choice = input("Install missing packages? (Y/n): ").strip().lower()
        if install_choice == 'n':
            print("Installation cancelled by user.")
            return False
        
        print("\n⬇️ Installing packages...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing_packages, 
                         check=True)
            print("✓ All packages installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install packages")
            return False
    
    def select_application(self):
        """Let user select which application to run"""
        print("\n" + "=" * 40)
        print("   🎯 Choose Application Version")
        print("=" * 40)
        
        for key, app in self.apps.items():
            recommended = " (Recommended)" if app["recommended"] else ""
            print(f"{key}. {app['name']}{recommended}")
        
        print()
        while True:
            choice = input(f"Select version (1-{len(self.apps)}): ").strip()
            if choice in self.apps:
                return self.apps[choice]
            print("❌ Invalid selection. Please try again.")
    
    def run_application(self, app):
        """Run the selected application"""
        app_file = app["file"]
        if not os.path.exists(app_file):
            print(f"❌ Application file '{app_file}' not found")
            return False
        
        print(f"\n🚀 Starting {app['name']}...")
        try:
            subprocess.run([sys.executable, app_file], check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"\n❌ Application error (Exit code: {e.returncode})")
            print("\nPossible solutions:")
            print("1. Check if all dependencies are correctly installed")
            print("2. Verify Python version compatibility (3.7+)")
            print("3. Check if ClickHouse database is accessible")
            print("4. Ensure TongDaXin path is correctly configured")
            return False
    
    def run(self):
        """Main launcher logic"""
        self.print_header()
        
        # Check conda
        if not self.check_conda():
            input("Press Enter to exit...")
            return
        
        # Select environment
        env_name = self.select_environment()
        if not self.activate_environment(env_name):
            input("Press Enter to exit...")
            return
        
        # Check and install requirements
        missing_packages = self.check_requirements()
        if missing_packages:
            if not self.install_requirements(missing_packages):
                input("Press Enter to exit...")
                return
        else:
            print("✓ All required packages are already installed")
        
        # Select and run application
        app = self.select_application()
        self.run_application(app)
        
        print("\n" + "=" * 40)
        print("Application finished.")
        input("Press Enter to exit...")


if __name__ == "__main__":
    launcher = SmartLauncher()
    launcher.run()
