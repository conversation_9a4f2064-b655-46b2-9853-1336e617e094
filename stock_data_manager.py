"""
Stock data management module that wraps the existing functionality.
Provides a clean interface for the GUI to interact with stock data operations.
"""

import os
import csv
import re
import pandas as pd
from clickhouse_driver import Client
from mootdx.reader import Reader
from tqdm import tqdm
import concurrent.futures
from concurrent.futures import Thr<PERSON><PERSON>ool<PERSON>xecutor
from typing import List, Tuple, Callable, Optional
from database_config import DatabaseConfig


class StockDataManager:
    """Manages stock data operations including list generation and data updates."""
    
    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.progress_callback: Optional[Callable] = None
        self.log_callback: Optional[Callable] = None
        
    def set_progress_callback(self, callback: Callable[[int, int, str], None]) -> None:
        """Set callback for progress updates. Callback receives (current, total, message)."""
        self.progress_callback = callback
    
    def set_log_callback(self, callback: Callable[[str], None]) -> None:
        """Set callback for log messages."""
        self.log_callback = callback
    
    def _log(self, message: str) -> None:
        """Internal logging method."""
        if self.log_callback:
            self.log_callback(message)
        print(message)
    
    def _update_progress(self, current: int, total: int, message: str = "") -> None:
        """Internal progress update method."""
        if self.progress_callback:
            self.progress_callback(current, total, message)
    
    def generate_stock_list(self) -> Tuple[bool, str, int]:
        """
        Generate stock list from TongDaXin directories.
        Returns: (success, message, count)
        """
        try:
            tdx_path = self.db_config.get_tdx_path()
            folders = [
                os.path.join(tdx_path, 'vipdoc/sh/lday'),
                os.path.join(tdx_path, 'vipdoc/sz/lday')
            ]
            
            csv_filename = 'Stock_List_Update.csv'
            prefixes = ['00', '30', '60', '68', '43', '83', '87', '92']
            stock_count = 0
            
            self._log("开始生成股票列表...")
            
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                csvwriter = csv.writer(csvfile)
                csvwriter.writerow(['Symbol'])
                
                for i, folder in enumerate(folders):
                    if not os.path.exists(folder):
                        self._log(f"警告：文件夹不存在 {folder}")
                        continue
                    
                    self._log(f"扫描文件夹：{folder}")
                    files = [f for f in os.listdir(folder) if f.endswith('.day')]
                    
                    for j, filename in enumerate(files):
                        self._update_progress(
                            i * len(files) + j, 
                            len(folders) * len(files), 
                            f"扫描文件：{filename}"
                        )
                        
                        numbers = re.findall(r'\d+', filename)
                        for number in numbers:
                            if any(number.startswith(prefix) for prefix in prefixes):
                                csvwriter.writerow([number])
                                stock_count += 1
                                break
            
            self._log(f"股票列表生成完成，共找到 {stock_count} 只股票")
            return True, f"成功生成股票列表，共 {stock_count} 只股票", stock_count
            
        except Exception as e:
            error_msg = f"生成股票列表失败：{str(e)}"
            self._log(error_msg)
            return False, error_msg, 0
    
    def check_daily_table(self, client: Client) -> None:
        """Check and create daily_data table if not exists."""
        if not client.execute("EXISTS TABLE daily_data")[0][0]:
            client.execute("""
                CREATE TABLE IF NOT EXISTS daily_data (
                    symbol String,
                    date Date,
                    open Float64,
                    high Float64,
                    low Float64,
                    close Float64,
                    amount Float64,
                    volume Float64
                ) ENGINE = ReplacingMergeTree()
                ORDER BY (symbol, date)
            """)
            self._log("创建 daily_data 表")
    
    def check_minute_table(self, client: Client) -> None:
        """Check and create minute_time_data table if not exists."""
        if not client.execute("EXISTS TABLE minute_time_data")[0][0]:
            client.execute("""
                CREATE TABLE IF NOT EXISTS minute_time_data (
                    symbol String,
                    date DateTime,
                    open Float64,
                    high Float64,
                    low Float64,
                    close Float64,
                    amount Float64,
                    volume Float64,
                    PRIMARY KEY (symbol, toStartOfMinute(date))
                ) ENGINE = ReplacingMergeTree()
                ORDER BY (symbol, toStartOfMinute(date))
            """)
            self._log("创建 minute_time_data 表")
    
    def load_stock_list(self) -> Tuple[bool, List[str], str]:
        """
        Load stock list from CSV file.
        Returns: (success, stock_list, message)
        """
        try:
            if not os.path.exists('Stock_List_Update.csv'):
                return False, [], "股票列表文件不存在，请先生成股票列表"
            
            df = pd.read_csv('Stock_List_Update.csv', usecols=[0], dtype={0: str})
            df = df.dropna()
            df.iloc[:, 0] = df.iloc[:, 0].str.zfill(6)
            stock_list = df.iloc[:, 0].tolist()
            
            return True, stock_list, f"成功加载 {len(stock_list)} 只股票"
            
        except Exception as e:
            error_msg = f"加载股票列表失败：{str(e)}"
            return False, [], error_msg

    def update_daily_data(self) -> Tuple[bool, str, dict]:
        """
        Update daily K-line data.
        Returns: (success, message, statistics)
        """
        try:
            # Load stock list
            success, stock_list, msg = self.load_stock_list()
            if not success:
                return False, msg, {}

            self._log("开始更新日线数据...")

            # Create main client and check table
            main_client = self.db_config.create_client()
            if not main_client:
                return False, "无法创建数据库连接", {}

            self.check_daily_table(main_client)

            # Create Reader object
            reader = Reader.factory(market='std', tdxdir=self.db_config.get_tdx_path())

            # Statistics
            inserted_count = 0
            skipped_count = 0
            error_count = 0

            def process_symbol(symbol: str) -> Tuple[int, int, int]:
                """Process single symbol for daily data."""
                try:
                    client = self.db_config.create_client()
                    if not client:
                        return 0, 0, 1

                    # Get daily data
                    daily_data = reader.daily(symbol=symbol)
                    if daily_data is None or daily_data.empty:
                        return 0, 0, 0

                    # Process data
                    daily_data.reset_index(inplace=True)
                    daily_data.insert(0, 'symbol', symbol)
                    daily_data['volume'] = daily_data['volume'].astype(float)
                    daily_data['amount'] = daily_data['amount'].astype(float)
                    daily_data.dropna(axis=1, how='all', inplace=True)
                    daily_data.fillna(0, inplace=True)

                    # Check existing data
                    latest_date = client.execute(
                        "SELECT MAX(date) FROM daily_data WHERE symbol = %(symbol)s",
                        {'symbol': symbol}
                    )[0][0]

                    if latest_date is None:
                        latest_date = pd.to_datetime('1970-01-01')

                    daily_data['date'] = pd.to_datetime(daily_data['date'])
                    latest_date = pd.to_datetime(latest_date)

                    # Filter new data
                    new_data = daily_data[daily_data['date'] > latest_date]

                    if not new_data.empty:
                        columns = ['symbol', 'date', 'open', 'high', 'low', 'close', 'amount', 'volume']
                        values = new_data[columns].values.tolist()
                        client.execute("INSERT INTO daily_data VALUES", values)
                        return len(new_data), 0, 0
                    else:
                        return 0, len(daily_data), 0

                except Exception as e:
                    return 0, 0, 1

            # Process with thread pool
            max_workers = self.db_config.get_max_workers()
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(process_symbol, symbol): symbol for symbol in stock_list}

                for i, future in enumerate(concurrent.futures.as_completed(futures)):
                    symbol = futures[future]
                    try:
                        ins, skip, err = future.result()
                        inserted_count += ins
                        skipped_count += skip
                        error_count += err

                        self._update_progress(
                            i + 1, len(stock_list),
                            f"处理股票 {symbol} - 插入:{ins}, 跳过:{skip}"
                        )
                    except Exception as e:
                        error_count += 1
                        self._log(f"处理股票 {symbol} 时出错: {e}")

            # Get final statistics
            total_rows = main_client.execute('SELECT COUNT(*) FROM daily_data')[0][0]

            stats = {
                'total_rows': total_rows,
                'inserted_count': inserted_count,
                'skipped_count': skipped_count,
                'error_count': error_count
            }

            message = f"日线数据更新完成！总行数: {total_rows}, 新增: {inserted_count}, 跳过: {skipped_count}, 错误: {error_count}"
            self._log(message)

            return True, message, stats

        except Exception as e:
            error_msg = f"更新日线数据失败：{str(e)}"
            self._log(error_msg)
            return False, error_msg, {}

    def update_minute_data(self) -> Tuple[bool, str, dict]:
        """
        Update minute-level data.
        Returns: (success, message, statistics)
        """
        try:
            # Load stock list
            success, stock_list, msg = self.load_stock_list()
            if not success:
                return False, msg, {}

            self._log("开始更新分时数据...")

            # Create main client and check table
            main_client = self.db_config.create_client()
            if not main_client:
                return False, "无法创建数据库连接", {}

            self.check_minute_table(main_client)

            # Create Reader object
            reader = Reader.factory(market='std', tdxdir=self.db_config.get_tdx_path())

            # Statistics
            inserted_count = 0
            skipped_count = 0
            error_count = 0

            def process_symbol(symbol: str) -> Tuple[int, int, int]:
                """Process single symbol for minute data."""
                try:
                    client = self.db_config.create_client()
                    if not client:
                        return 0, 0, 1

                    # Get minute data
                    minute_data = reader.minute(symbol=symbol)
                    if minute_data is None or minute_data.empty:
                        return 0, 0, 0

                    # Process data
                    minute_data.reset_index(inplace=True)
                    minute_data.insert(0, 'symbol', symbol)
                    minute_data['volume'] = minute_data['volume'].astype(float)
                    minute_data['amount'] = minute_data['amount'].astype(float)
                    minute_data.dropna(axis=1, how='all', inplace=True)
                    minute_data.fillna(0, inplace=True)
                    minute_data['date'] = pd.to_datetime(minute_data['date'])

                    # Query existing data
                    existing_dates = client.execute(
                        "SELECT toStartOfMinute(date) FROM minute_time_data WHERE symbol = %(symbol)s",
                        {'symbol': symbol}
                    )
                    existing_dates = pd.to_datetime([row[0] for row in existing_dates])

                    # Filter new data by minute
                    minute_data['minute'] = minute_data['date'].dt.floor('min')
                    new_data = minute_data[~minute_data['minute'].isin(existing_dates)]

                    if not new_data.empty:
                        columns = ['symbol', 'date', 'open', 'high', 'low', 'close', 'amount', 'volume']
                        values = new_data[columns].values.tolist()
                        client.execute("INSERT INTO minute_time_data VALUES", values)
                        return len(new_data), 0, 0
                    else:
                        return 0, len(minute_data), 0

                except Exception as e:
                    return 0, 0, 1

            # Process with thread pool
            max_workers = self.db_config.get_max_workers()
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(process_symbol, symbol): symbol for symbol in stock_list}

                for i, future in enumerate(concurrent.futures.as_completed(futures)):
                    symbol = futures[future]
                    try:
                        ins, skip, err = future.result()
                        inserted_count += ins
                        skipped_count += skip
                        error_count += err

                        self._update_progress(
                            i + 1, len(stock_list),
                            f"处理股票 {symbol} - 插入:{ins}, 跳过:{skip}"
                        )
                    except Exception as e:
                        error_count += 1
                        self._log(f"处理股票 {symbol} 时出错: {e}")

            # Get final statistics
            total_rows = main_client.execute('SELECT COUNT(*) FROM minute_time_data')[0][0]

            stats = {
                'total_rows': total_rows,
                'inserted_count': inserted_count,
                'skipped_count': skipped_count,
                'error_count': error_count
            }

            message = f"分时数据更新完成！总行数: {total_rows}, 新增: {inserted_count}, 跳过: {skipped_count}, 错误: {error_count}"
            self._log(message)

            return True, message, stats

        except Exception as e:
            error_msg = f"更新分时数据失败：{str(e)}"
            self._log(error_msg)
            return False, error_msg, {}

    def update_daily_data(self) -> Tuple[bool, str, dict]:
        """
        Update daily K-line data.
        Returns: (success, message, statistics)
        """
        try:
            # Load stock list
            success, stock_list, msg = self.load_stock_list()
            if not success:
                return False, msg, {}

            self._log("开始更新日线数据...")

            # Create main client and check table
            main_client = self.db_config.create_client()
            if not main_client:
                return False, "无法创建数据库连接", {}

            self.check_daily_table(main_client)

            # Create Reader object
            reader = Reader.factory(market='std', tdxdir=self.db_config.get_tdx_path())

            # Statistics
            inserted_count = 0
            skipped_count = 0
            error_count = 0

            def process_symbol(symbol: str) -> Tuple[int, int, int]:
                """Process single symbol for daily data."""
                try:
                    client = self.db_config.create_client()
                    if not client:
                        return 0, 0, 1

                    # Get daily data
                    daily_data = reader.daily(symbol=symbol)
                    if daily_data is None or daily_data.empty:
                        return 0, 0, 0

                    # Process data
                    daily_data.reset_index(inplace=True)
                    daily_data.insert(0, 'symbol', symbol)
                    daily_data['volume'] = daily_data['volume'].astype(float)
                    daily_data['amount'] = daily_data['amount'].astype(float)
                    daily_data.dropna(axis=1, how='all', inplace=True)
                    daily_data.fillna(0, inplace=True)

                    # Check existing data
                    latest_date = client.execute(
                        "SELECT MAX(date) FROM daily_data WHERE symbol = %(symbol)s",
                        {'symbol': symbol}
                    )[0][0]

                    if latest_date is None:
                        latest_date = pd.to_datetime('1970-01-01')

                    daily_data['date'] = pd.to_datetime(daily_data['date'])
                    latest_date = pd.to_datetime(latest_date)

                    # Filter new data
                    new_data = daily_data[daily_data['date'] > latest_date]

                    if not new_data.empty:
                        columns = ['symbol', 'date', 'open', 'high', 'low', 'close', 'amount', 'volume']
                        values = new_data[columns].values.tolist()
                        client.execute("INSERT INTO daily_data VALUES", values)
                        return len(new_data), 0, 0
                    else:
                        return 0, len(daily_data), 0

                except Exception as e:
                    self._log(f"处理股票 {symbol} 时出错: {e}")
                    return 0, 0, 1

            # Process with thread pool
            max_workers = self.db_config.get_max_workers()
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(process_symbol, symbol): symbol for symbol in stock_list}

                for i, future in enumerate(concurrent.futures.as_completed(futures)):
                    symbol = futures[future]
                    try:
                        ins, skip, err = future.result()
                        inserted_count += ins
                        skipped_count += skip
                        error_count += err

                        self._update_progress(
                            i + 1, len(stock_list),
                            f"处理股票 {symbol} - 插入:{ins}, 跳过:{skip}"
                        )
                    except Exception as e:
                        error_count += 1
                        self._log(f"处理股票 {symbol} 时出错: {e}")

            # Get final statistics
            total_rows = main_client.execute('SELECT COUNT(*) FROM daily_data')[0][0]

            stats = {
                'total_rows': total_rows,
                'inserted_count': inserted_count,
                'skipped_count': skipped_count,
                'error_count': error_count
            }

            message = f"日线数据更新完成！总行数: {total_rows}, 新增: {inserted_count}, 跳过: {skipped_count}, 错误: {error_count}"
            self._log(message)

            return True, message, stats

        except Exception as e:
            error_msg = f"更新日线数据失败：{str(e)}"
            self._log(error_msg)
            return False, error_msg, {}
