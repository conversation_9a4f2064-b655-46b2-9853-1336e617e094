import pandas as pd
from clickhouse_driver import Client
from mootdx.reader import Reader
from tqdm import tqdm
import concurrent.futures 
from concurrent.futures import ThreadPoolExecutor

# 检查是否存在minute_time_data表
def check_table(client):
    if not client.execute("EXISTS TABLE minute_time_data")[0][0]:
        # 创建minute_time_data表
        client.execute("""
            CREATE TABLE IF NOT EXISTS minute_time_data (
                symbol String,
                date DateTime,
                open Float64,
                high Float64,
                low Float64,
                close Float64,
                amount Float64,
                volume Float64
            ) ENGINE = ReplacingMergeTree()
            ORDER BY (symbol, date)
        """)

# 创建Reader对象
reader = Reader.factory(market='std', tdxdir='D:/通达信金融终端')

# 读取CSV文件，只取第一列，且确保股票代码为6位
df = pd.read_csv('Stock_List_Update.csv', usecols=[0], dtype={0:str}) # 显性地将此列设置为str类型
df = df.dropna()    # 移除空值行
df.iloc[:,0] = df.iloc[:,0].str.zfill(6)

# 定义计数器，用于统计插入和跳过的数据
inserted_count = 0
skipped_count = 0

# 定义处理函数
def process_symbol(symbol):
    global inserted_count, skipped_count
    # 每个线程创建自己的ClickHouse客户端连接
    client = Client(host='***********', user='root', password='root', port=9000)

    try:
        # 获取分时数据
        minute_time_data = reader.minute(symbol=symbol)

        # 如果没有数据，直接返回
        if minute_time_data is None or minute_time_data.empty:
            return

        # 重置索引，使时间成为一列
        minute_time_data.reset_index(inplace=True)

        # 在最前面插入一列，列名为'symbol'，所有行的值都是symbol
        minute_time_data.insert(0, 'symbol', symbol)

        # 数据类型转换
        minute_time_data['volume'] = minute_time_data['volume'].astype(float)
        minute_time_data['amount'] = minute_time_data['amount'].astype(float)

        # 删除全为NA的列
        minute_time_data.dropna(axis=1, how='all', inplace=True)
        
        # 填充剩余的NA值
        minute_time_data.fillna(0, inplace=True)

        # 查询现有的数据库数据，以找出需要插入的新数据
        existing_dates = client.execute(
            "SELECT date FROM minute_time_data WHERE symbol = %(symbol)s",
            {'symbol': symbol}
        )
        existing_dates = pd.to_datetime([row[0] for row in existing_dates])  # 将已有日期放入集合中，转换为datetime类型

        # 确保minute_time_data中的'date'列为datetime类型
        minute_time_data['date'] = pd.to_datetime(minute_time_data['date'])

        # 过滤掉已有数据的日期
        new_data = minute_time_data[~minute_time_data['date'].isin(existing_dates)]

        # 如果有新的数据则插入
        if not new_data.empty:
            columns = ['symbol', 'date', 'open', 'high', 'low', 'close', 'amount', 'volume']
            values = new_data[columns].values.tolist()
            client.execute("INSERT INTO minute_time_data VALUES", values)
            inserted_count += len(new_data)
        else:
            skipped_count += len(minute_time_data)

    except Exception as e:
        pass

# 处理所有symbol
symbols = df.iloc[:,0].tolist()  # 已经是格式化后的6位股票代码

# 创建主连接，用于检查表是否存在
main_client = Client(host='***********', user='root', password='root', port=9000)
check_table(main_client)

# 使用并发执行处理函数，并显示进度条
with ThreadPoolExecutor(max_workers=10) as executor:
    futures = {executor.submit(process_symbol, symbol): symbol for symbol in symbols}
    for future in tqdm(concurrent.futures.as_completed(futures), total=len(symbols), desc='Processing symbols'):
        pass

# 所有symbol处理完毕后，查询数据库中的minute_time_data的总行数
total_rows = main_client.execute('SELECT COUNT(*) FROM minute_time_data')[0][0]
print(f'每日数据中的总行数: {total_rows}')
print(f'插入的新记录总数: {inserted_count}')
print(f'跳过（已存在）的行总数: {skipped_count}')