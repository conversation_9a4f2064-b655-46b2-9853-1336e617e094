"""
Stock Data Management GUI Application
A comprehensive interface for managing stock data downloads and updates.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import logging
from datetime import datetime
import os
from database_config import DatabaseConfig
from stock_data_manager import StockDataManager


class StockDataGUI:
    """Main GUI application for stock data management."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("股票数据管理系统")
        self.root.geometry("800x700")
        
        # Initialize components
        self.db_config = DatabaseConfig()
        self.data_manager = StockDataManager(self.db_config)
        
        # Set up callbacks
        self.data_manager.set_progress_callback(self.update_progress)
        self.data_manager.set_log_callback(self.log_message)
        
        # Initialize logging
        self.setup_logging()
        
        # Create GUI
        self.create_widgets()
        
        # Load saved configuration
        self.load_config_to_gui()
    
    def setup_logging(self):
        """Set up logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('stock_data.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_widgets(self):
        """Create and arrange GUI widgets."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_config_tab()
        self.create_data_tab()
        self.create_log_tab()
    
    def create_config_tab(self):
        """Create database configuration tab."""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="数据库配置")
        
        # Database configuration section
        db_group = ttk.LabelFrame(config_frame, text="ClickHouse 数据库配置", padding=10)
        db_group.pack(fill=tk.X, padx=10, pady=5)
        
        # Host
        ttk.Label(db_group, text="主机地址:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.host_var = tk.StringVar()
        ttk.Entry(db_group, textvariable=self.host_var, width=30).grid(row=0, column=1, padx=5, pady=2)
        
        # Port
        ttk.Label(db_group, text="端口:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.port_var = tk.StringVar()
        ttk.Entry(db_group, textvariable=self.port_var, width=30).grid(row=1, column=1, padx=5, pady=2)
        
        # Username
        ttk.Label(db_group, text="用户名:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.user_var = tk.StringVar()
        ttk.Entry(db_group, textvariable=self.user_var, width=30).grid(row=2, column=1, padx=5, pady=2)
        
        # Password
        ttk.Label(db_group, text="密码:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar()
        ttk.Entry(db_group, textvariable=self.password_var, show="*", width=30).grid(row=3, column=1, padx=5, pady=2)
        
        # Database
        ttk.Label(db_group, text="数据库:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.database_var = tk.StringVar()
        ttk.Entry(db_group, textvariable=self.database_var, width=30).grid(row=4, column=1, padx=5, pady=2)
        
        # Buttons
        button_frame = ttk.Frame(db_group)
        button_frame.grid(row=5, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="测试连接", command=self.test_connection).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        
        # TongDaXin configuration section
        tdx_group = ttk.LabelFrame(config_frame, text="通达信配置", padding=10)
        tdx_group.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(tdx_group, text="通达信路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.tdx_path_var = tk.StringVar()
        ttk.Entry(tdx_group, textvariable=self.tdx_path_var, width=40).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(tdx_group, text="浏览", command=self.browse_tdx_path).grid(row=0, column=2, padx=5, pady=2)
        
        # Performance settings
        perf_group = ttk.LabelFrame(config_frame, text="性能设置", padding=10)
        perf_group.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(perf_group, text="最大线程数:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.max_workers_var = tk.StringVar()
        ttk.Entry(perf_group, textvariable=self.max_workers_var, width=10).grid(row=0, column=1, padx=5, pady=2)
    
    def create_data_tab(self):
        """Create data management tab."""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="数据管理")
        
        # Stock list section
        stock_group = ttk.LabelFrame(data_frame, text="股票列表管理", padding=10)
        stock_group.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(stock_group, text="生成股票列表", command=self.generate_stock_list).pack(side=tk.LEFT, padx=5)
        
        self.stock_count_label = ttk.Label(stock_group, text="股票数量: 未知")
        self.stock_count_label.pack(side=tk.LEFT, padx=20)
        
        # Data update section
        update_group = ttk.LabelFrame(data_frame, text="数据更新", padding=10)
        update_group.pack(fill=tk.X, padx=10, pady=5)
        
        # Data type selection
        self.data_type_var = tk.StringVar(value="daily")
        ttk.Radiobutton(update_group, text="日线数据 (K线)", variable=self.data_type_var, 
                       value="daily").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Radiobutton(update_group, text="分时数据 (分钟级)", variable=self.data_type_var, 
                       value="minute").grid(row=1, column=0, sticky=tk.W, pady=2)
        
        # Update button
        self.update_button = ttk.Button(update_group, text="开始更新数据", command=self.start_data_update)
        self.update_button.grid(row=2, column=0, pady=10)
        
        # Progress section
        progress_group = ttk.LabelFrame(data_frame, text="进度信息", padding=10)
        progress_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_group, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # Progress label
        self.progress_label = ttk.Label(progress_group, text="就绪")
        self.progress_label.pack(pady=2)
        
        # Statistics
        self.stats_text = scrolledtext.ScrolledText(progress_group, height=8, width=70)
        self.stats_text.pack(fill=tk.BOTH, expand=True, pady=5)
    
    def create_log_tab(self):
        """Create logging tab."""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="日志")
        
        # Log display
        self.log_text = scrolledtext.ScrolledText(log_frame, height=30, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(log_controls, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_controls, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)

    def load_config_to_gui(self):
        """Load saved configuration to GUI."""
        db_config = self.db_config.get_db_config()
        self.host_var.set(db_config.get("host", ""))
        self.port_var.set(str(db_config.get("port", "")))
        self.user_var.set(db_config.get("user", ""))
        self.password_var.set(db_config.get("password", ""))
        self.database_var.set(db_config.get("database", ""))

        self.tdx_path_var.set(self.db_config.get_tdx_path())
        self.max_workers_var.set(str(self.db_config.get_max_workers()))

    def save_config(self):
        """Save configuration from GUI."""
        try:
            # Validate port
            port = int(self.port_var.get()) if self.port_var.get() else 9000
            max_workers = int(self.max_workers_var.get()) if self.max_workers_var.get() else 10

            # Update database config
            self.db_config.update_db_config(
                host=self.host_var.get(),
                port=port,
                user=self.user_var.get(),
                password=self.password_var.get(),
                database=self.database_var.get() or "default"
            )

            # Update other settings
            self.db_config.set_tdx_path(self.tdx_path_var.get())
            self.db_config.set_max_workers(max_workers)

            # Save to file
            if self.db_config.save_config():
                messagebox.showinfo("成功", "配置已保存！")
                self.log_message("配置已保存")
            else:
                messagebox.showerror("错误", "保存配置失败！")

        except ValueError as e:
            messagebox.showerror("错误", f"配置参数错误：{e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置时出错：{e}")

    def test_connection(self):
        """Test database connection."""
        try:
            # Update config with current GUI values
            self.save_config()

            # Test connection
            success, message = self.db_config.test_connection()

            if success:
                messagebox.showinfo("连接测试", message)
                self.log_message(f"数据库连接测试成功: {message}")
            else:
                messagebox.showerror("连接测试", message)
                self.log_message(f"数据库连接测试失败: {message}")

        except Exception as e:
            error_msg = f"连接测试出错：{str(e)}"
            messagebox.showerror("错误", error_msg)
            self.log_message(error_msg)

    def browse_tdx_path(self):
        """Browse for TongDaXin installation path."""
        path = filedialog.askdirectory(title="选择通达信安装目录")
        if path:
            self.tdx_path_var.set(path)

    def generate_stock_list(self):
        """Generate stock list in a separate thread."""
        def run_generation():
            try:
                self.update_button.config(state='disabled')
                success, message, count = self.data_manager.generate_stock_list()

                if success:
                    self.stock_count_label.config(text=f"股票数量: {count}")
                    messagebox.showinfo("成功", message)
                else:
                    messagebox.showerror("错误", message)

            except Exception as e:
                error_msg = f"生成股票列表时出错：{str(e)}"
                messagebox.showerror("错误", error_msg)
                self.log_message(error_msg)
            finally:
                self.update_button.config(state='normal')

        threading.Thread(target=run_generation, daemon=True).start()

    def start_data_update(self):
        """Start data update in a separate thread."""
        def run_update():
            try:
                self.update_button.config(state='disabled')
                self.progress_var.set(0)
                self.progress_label.config(text="准备更新...")

                data_type = self.data_type_var.get()

                if data_type == "daily":
                    success, message, stats = self.data_manager.update_daily_data()
                elif data_type == "minute":
                    success, message, stats = self.data_manager.update_minute_data()
                else:
                    success, message, stats = False, "未知的数据类型", {}

                # Display results
                if success:
                    self.display_statistics(stats)
                    messagebox.showinfo("成功", message)
                else:
                    messagebox.showerror("错误", message)

            except Exception as e:
                error_msg = f"更新数据时出错：{str(e)}"
                messagebox.showerror("错误", error_msg)
                self.log_message(error_msg)
            finally:
                self.update_button.config(state='normal')
                self.progress_var.set(100)
                self.progress_label.config(text="完成")

        threading.Thread(target=run_update, daemon=True).start()

    def update_progress(self, current: int, total: int, message: str = ""):
        """Update progress bar and label."""
        if total > 0:
            progress = (current / total) * 100
            self.progress_var.set(progress)

        if message:
            self.progress_label.config(text=f"{message} ({current}/{total})")
        else:
            self.progress_label.config(text=f"进度: {current}/{total}")

    def display_statistics(self, stats: dict):
        """Display statistics in the stats text area."""
        self.stats_text.delete(1.0, tk.END)

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.stats_text.insert(tk.END, f"更新完成时间: {timestamp}\n\n")

        for key, value in stats.items():
            if key == 'total_rows':
                self.stats_text.insert(tk.END, f"数据库总行数: {value:,}\n")
            elif key == 'inserted_count':
                self.stats_text.insert(tk.END, f"新增记录数: {value:,}\n")
            elif key == 'skipped_count':
                self.stats_text.insert(tk.END, f"跳过记录数: {value:,}\n")
            elif key == 'error_count':
                self.stats_text.insert(tk.END, f"错误记录数: {value:,}\n")

        self.stats_text.insert(tk.END, "\n" + "="*50 + "\n")

    def log_message(self, message: str):
        """Add message to log display."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # Also log to file
        self.logger.info(message)

    def clear_log(self):
        """Clear the log display."""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """Save log to file."""
        try:
            filename = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已保存到: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")


def main():
    """Main function to run the application."""
    root = tk.Tk()
    app = StockDataGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()

    def load_config_to_gui(self):
        """Load saved configuration to GUI."""
        db_config = self.db_config.get_db_config()
        self.host_var.set(db_config.get("host", ""))
        self.port_var.set(str(db_config.get("port", "")))
        self.user_var.set(db_config.get("user", ""))
        self.password_var.set(db_config.get("password", ""))
        self.database_var.set(db_config.get("database", ""))

        self.tdx_path_var.set(self.db_config.get_tdx_path())
        self.max_workers_var.set(str(self.db_config.get_max_workers()))

    def save_config(self):
        """Save configuration from GUI."""
        try:
            # Validate port
            port = int(self.port_var.get()) if self.port_var.get() else 9000
            max_workers = int(self.max_workers_var.get()) if self.max_workers_var.get() else 10

            # Update database config
            self.db_config.update_db_config(
                host=self.host_var.get(),
                port=port,
                user=self.user_var.get(),
                password=self.password_var.get(),
                database=self.database_var.get() or "default"
            )

            # Update other settings
            self.db_config.set_tdx_path(self.tdx_path_var.get())
            self.db_config.set_max_workers(max_workers)

            # Save to file
            if self.db_config.save_config():
                messagebox.showinfo("成功", "配置已保存！")
                self.log_message("配置已保存")
            else:
                messagebox.showerror("错误", "保存配置失败！")

        except ValueError as e:
            messagebox.showerror("错误", f"配置参数错误：{e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置时出错：{e}")

    def test_connection(self):
        """Test database connection."""
        try:
            # Update config with current GUI values
            self.save_config()

            # Test connection
            success, message = self.db_config.test_connection()

            if success:
                messagebox.showinfo("连接测试", message)
                self.log_message(f"数据库连接测试成功: {message}")
            else:
                messagebox.showerror("连接测试", message)
                self.log_message(f"数据库连接测试失败: {message}")

        except Exception as e:
            error_msg = f"连接测试出错：{str(e)}"
            messagebox.showerror("错误", error_msg)
            self.log_message(error_msg)

    def browse_tdx_path(self):
        """Browse for TongDaXin installation path."""
        path = filedialog.askdirectory(title="选择通达信安装目录")
        if path:
            self.tdx_path_var.set(path)

    def generate_stock_list(self):
        """Generate stock list in a separate thread."""
        def run_generation():
            try:
                self.update_button.config(state='disabled')
                success, message, count = self.data_manager.generate_stock_list()

                if success:
                    self.stock_count_label.config(text=f"股票数量: {count}")
                    messagebox.showinfo("成功", message)
                else:
                    messagebox.showerror("错误", message)

            except Exception as e:
                error_msg = f"生成股票列表时出错：{str(e)}"
                messagebox.showerror("错误", error_msg)
                self.log_message(error_msg)
            finally:
                self.update_button.config(state='normal')

        threading.Thread(target=run_generation, daemon=True).start()

    def start_data_update(self):
        """Start data update in a separate thread."""
        def run_update():
            try:
                self.update_button.config(state='disabled')
                self.progress_var.set(0)
                self.progress_label.config(text="准备更新...")

                data_type = self.data_type_var.get()

                if data_type == "daily":
                    success, message, stats = self.data_manager.update_daily_data()
                elif data_type == "minute":
                    success, message, stats = self.data_manager.update_minute_data()
                else:
                    success, message, stats = False, "未知的数据类型", {}

                # Display results
                if success:
                    self.display_statistics(stats)
                    messagebox.showinfo("成功", message)
                else:
                    messagebox.showerror("错误", message)

            except Exception as e:
                error_msg = f"更新数据时出错：{str(e)}"
                messagebox.showerror("错误", error_msg)
                self.log_message(error_msg)
            finally:
                self.update_button.config(state='normal')
                self.progress_var.set(100)
                self.progress_label.config(text="完成")

        threading.Thread(target=run_update, daemon=True).start()
