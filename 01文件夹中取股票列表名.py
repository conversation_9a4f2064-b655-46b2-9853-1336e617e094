import os
import csv
import re

# 文件夹路径
folders = [
    'D:/通达信金融终端/vipdoc/sh/lday',
    'D:/通达信金融终端/vipdoc/sz/lday'  
]

# CSV文件名
csv_filename = 'Stock_List_Update.csv'

# 保存文件名到CSV
with open(csv_filename, 'w', newline='') as csvfile:
    csvwriter = csv.writer(csvfile)
    # 写入标题行
    csvwriter.writerow(['Symbol'])
    
    prefixes = ['00', '30', '60', '68','43','83','87','92']  # 需要的数字前缀

    for folder in folders:
        # 遍历文件夹中的文件
        for filename in os.listdir(folder):
            if filename.endswith('.day'):
                # 使用正则表达式匹配数字
                numbers = re.findall(r'\d+', filename)
                if numbers:
                    # 添加一个条件来查找以特定前缀开头的数字
                    for number in numbers:
                        if any(number.startswith(prefix) for prefix in prefixes):
                            # 如果找到了合适的数字，写入CSV
                            csvwriter.writerow([number])

print(f'文件名已保存到 {csv_filename}')